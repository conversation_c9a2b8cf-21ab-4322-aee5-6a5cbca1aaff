using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;
using Winmug.Core.Authentication;
using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Implementation of SmugMug API client
/// </summary>
public class SmugMugApiClient : ISmugMugApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ILogger<SmugMugApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    private const string BaseApiUrl = "https://api.smugmug.com/api/v2";

    public SmugMugApiClient(
        HttpClient httpClient,
        ISmugMugAuthenticationService authService,
        ILogger<SmugMugApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<SmugMugUser> GetAuthenticatedUserAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting authenticated user information");
        
        var url = $"{BaseApiUrl}!authuser";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugUser>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException("Failed to get authenticated user information");
        }

        return response.Response;
    }

    public async Task<bool> VerifyPrivateAccessAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Verifying private access level");

            // Try to access the authenticated user endpoint which should return private data
            var url = $"{BaseApiUrl}!authuser";
            var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<dynamic>>(HttpMethod.Get, url, cancellationToken);

            if (response?.Response == null)
            {
                _logger.LogWarning("Failed to get authenticated user response");
                return false;
            }

            // Check if we have private access by looking for ResponseLevel
            var responseJson = JsonSerializer.Serialize(response.Response);
            var responseObj = JsonSerializer.Deserialize<JsonElement>(responseJson);

            if (responseObj.TryGetProperty("User", out var userElement) &&
                userElement.TryGetProperty("ResponseLevel", out var responseLevelElement))
            {
                var responseLevel = responseLevelElement.GetString();
                _logger.LogInformation("Access level: {ResponseLevel}", responseLevel);

                // We should have "Full" access for private data, not just "Public"
                return !string.Equals(responseLevel, "Public", StringComparison.OrdinalIgnoreCase);
            }

            _logger.LogWarning("Could not determine response level from authenticated user response");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify private access");
            return false;
        }
    }

    public async Task<SmugMugNode> GetUserRootNodeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting user root node");
        
        var user = await GetAuthenticatedUserAsync(cancellationToken);
        if (user.Uris?.Node?.Uri == null)
        {
            throw new InvalidOperationException("User does not have a root node URI");
        }

        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, user.Uris.Node.Uri, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException("Failed to get user root node");
        }

        return response.Response;
    }

    public async Task<SmugMugNode> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting node: {NodeId}", nodeId);
        
        var url = $"{BaseApiUrl}/node/{nodeId}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugNode>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get node: {nodeId}");
        }

        return response.Response;
    }

    public async Task<List<SmugMugNode>> GetChildNodesAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting child nodes for: {NodeId}", nodeId);
        
        var url = $"{BaseApiUrl}/node/{nodeId}!children";
        var nodes = new List<SmugMugNode>();
        
        await foreach (var node in GetPagedResultsAsync<SmugMugNode>(url, cancellationToken))
        {
            nodes.Add(node);
        }

        _logger.LogDebug("Found {Count} child nodes for: {NodeId}", nodes.Count, nodeId);
        return nodes;
    }

    public async Task<List<SmugMugNode>> GetAllChildNodesRecursiveAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting all child nodes recursively for: {NodeId}", nodeId);
        
        var allNodes = new List<SmugMugNode>();
        var nodesToProcess = new Queue<string>();
        nodesToProcess.Enqueue(nodeId);

        while (nodesToProcess.Count > 0)
        {
            var currentNodeId = nodesToProcess.Dequeue();
            var childNodes = await GetChildNodesAsync(currentNodeId, cancellationToken);
            
            foreach (var childNode in childNodes)
            {
                allNodes.Add(childNode);
                
                // If it's a folder, add it to the queue for recursive processing
                if (childNode.IsFolder)
                {
                    nodesToProcess.Enqueue(childNode.NodeId);
                }
            }
        }

        _logger.LogDebug("Found {Count} total nodes recursively for: {NodeId}", allNodes.Count, nodeId);
        return allNodes;
    }

    public async Task<SmugMugAlbum> GetAlbumAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting album: {AlbumKey}", albumKey);
        
        var url = $"{BaseApiUrl}/album/{albumKey}";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugAlbum>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get album: {albumKey}");
        }

        return response.Response;
    }

    public async Task<List<SmugMugImage>> GetAlbumImagesAsync(string albumKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting images for album: {AlbumKey}", albumKey);
        
        var url = $"{BaseApiUrl}/album/{albumKey}!images";
        var images = new List<SmugMugImage>();
        
        await foreach (var image in GetPagedResultsAsync<SmugMugImage>(url, cancellationToken))
        {
            images.Add(image);
        }

        _logger.LogDebug("Found {Count} images in album: {AlbumKey}", images.Count, albumKey);
        return images;
    }

    public async Task<SmugMugImageSizes> GetImageSizeDetailsAsync(string imageKey, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting size details for image: {ImageKey}", imageKey);
        
        var url = $"{BaseApiUrl}/image/{imageKey}!sizedetails";
        var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugImageSizes>>(HttpMethod.Get, url, cancellationToken);
        
        if (response?.Response == null)
        {
            throw new InvalidOperationException($"Failed to get image size details: {imageKey}");
        }

        return response.Response;
    }

    public async Task<Stream> DownloadImageAsync(string imageUrl, CancellationToken cancellationToken = default)
    {
        return await DownloadImageAsync(imageUrl, null, cancellationToken);
    }

    public async Task<Stream> DownloadImageAsync(string imageUrl, IProgress<DownloadProgress>? progress, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Downloading image from: {ImageUrl}", imageUrl);

        var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, imageUrl);
        var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
        response.EnsureSuccessStatusCode();

        var totalBytes = response.Content.Headers.ContentLength;
        var responseStream = await response.Content.ReadAsStreamAsync(cancellationToken);

        if (progress == null)
        {
            return responseStream;
        }

        // Wrap the stream with progress reporting
        return new ProgressReportingStream(responseStream, totalBytes, progress);
    }

    private async Task<T?> SendAuthenticatedRequestAsync<T>(HttpMethod method, string url, CancellationToken cancellationToken)
    {
        var request = _authService.CreateAuthenticatedRequest(method, url);
        request.Headers.Add("Accept", "application/json");

        var response = await _httpClient.SendAsync(request, cancellationToken);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonSerializer.Deserialize<T>(content, _jsonOptions);
    }

    private async IAsyncEnumerable<T> GetPagedResultsAsync<T>(string initialUrl, [System.Runtime.CompilerServices.EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var url = initialUrl;

        while (!string.IsNullOrEmpty(url))
        {
            var response = await SendAuthenticatedRequestAsync<SmugMugApiResponse<SmugMugCollectionResponse<T>>>(HttpMethod.Get, url, cancellationToken);

            if (response?.Response == null)
            {
                yield break;
            }

            foreach (var item in response.Response.Items)
            {
                yield return item;
            }

            // Get next page URL
            url = response.Response.Pages?.NextPage;
        }
    }
}
