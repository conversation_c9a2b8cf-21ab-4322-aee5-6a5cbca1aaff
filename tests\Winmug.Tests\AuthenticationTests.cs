using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Winmug.Core.Authentication;
using Winmug.Core.Services;
using Xunit;

namespace Winmug.Tests;

public class AuthenticationTests
{
    [Fact]
    public void OAuthCredentials_IsAuthenticated_ReturnsFalseWhenTokensAreNull()
    {
        // Arrange
        var credentials = new OAuthCredentials
        {
            ConsumerKey = "test-key",
            ConsumerSecret = "test-secret"
        };

        // Act & Assert
        credentials.IsAuthenticated.Should().BeFalse();
    }

    [Fact]
    public void OAuthCredentials_IsAuthenticated_ReturnsTrueWhenTokensAreSet()
    {
        // Arrange
        var credentials = new OAuthCredentials
        {
            ConsumerKey = "test-key",
            ConsumerSecret = "test-secret",
            AccessToken = "access-token",
            AccessTokenSecret = "access-token-secret"
        };

        // Act & Assert
        credentials.IsAuthenticated.Should().BeTrue();
    }

    [Fact]
    public void OAuthSignatureGenerator_GenerateNonce_ReturnsNonEmptyString()
    {
        // Act
        var nonce = OAuthSignatureGenerator.GenerateNonce();

        // Assert
        nonce.Should().NotBeNullOrEmpty();
        nonce.Length.Should().Be(32); // GUID without hyphens
    }

    [Fact]
    public void OAuthSignatureGenerator_GenerateTimestamp_ReturnsValidTimestamp()
    {
        // Act
        var timestamp = OAuthSignatureGenerator.GenerateTimestamp();

        // Assert
        timestamp.Should().NotBeNullOrEmpty();
        long.TryParse(timestamp, out _).Should().BeTrue();
    }

    [Fact]
    public void OAuthSignatureGenerator_UrlEncode_EncodesSpecialCharacters()
    {
        // Arrange
        var input = "hello world!@#$%^&*()";

        // Act
        var encoded = OAuthSignatureGenerator.UrlEncode(input);

        // Assert
        encoded.Should().NotContain(" ");
        encoded.Should().NotContain("!");
        encoded.Should().NotContain("@");
    }

    [Fact]
    public async Task SmugMugAuthenticationService_IsAuthenticated_ReturnsFalseInitially()
    {
        // Arrange
        var httpClient = new HttpClient();
        var credentialStorage = new Mock<ISecureCredentialStorage>();
        var options = Options.Create(new SmugMugOAuthOptions
        {
            ConsumerKey = "test-key",
            ConsumerSecret = "test-secret"
        });
        var logger = new Mock<ILogger<SmugMugAuthenticationService>>();

        var authService = new SmugMugAuthenticationService(httpClient, credentialStorage.Object, options, logger.Object);

        // Act & Assert
        authService.IsAuthenticated.Should().BeFalse();
    }
}
