using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Windows;
using Winmug.Core.Authentication;
using Winmug.Core.Services;
using Winmug.ViewModels;
using Winmug.Views;

namespace Winmug;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // Build configuration
        var configuration = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
            .Build();

        // Build host
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                ConfigureServices(services, configuration);
            })
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.AddDebug();
                logging.SetMinimumLevel(LogLevel.Information);
            })
            .Build();

        await _host.StartAsync();

        // Create and show main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }
        base.OnExit(e);
    }

    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Configuration
        services.Configure<SmugMugOAuthOptions>(configuration.GetSection(SmugMugOAuthOptions.SectionName));

        // HTTP Client
        services.AddHttpClient<ISmugMugApiClient, SmugMugApiClient>(client =>
        {
            client.DefaultRequestHeaders.Add("User-Agent", "Winmug/1.0");
            client.Timeout = TimeSpan.FromMinutes(5);
        });

        // Core services
        services.AddSingleton<ISecureCredentialStorage, WindowsCredentialStorage>();
        services.AddSingleton<ISmugMugAuthenticationService, SmugMugAuthenticationService>();
        services.AddTransient<ISmugMugApiClient, SmugMugApiClient>();
        services.AddTransient<IDownloadManager, DownloadManager>();

        // ViewModels
        services.AddTransient<MainWindowViewModel>();

        // Views
        services.AddTransient<MainWindow>();
    }
}
