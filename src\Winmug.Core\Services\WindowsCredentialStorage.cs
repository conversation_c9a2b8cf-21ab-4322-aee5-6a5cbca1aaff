using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace Winmug.Core.Services;

/// <summary>
/// Windows-specific implementation of secure credential storage using Windows Credential Manager
/// </summary>
public class WindowsCredentialStorage : ISecureCredentialStorage
{
    private const string CredentialTarget = "Winmug_SmugMug_OAuth";
    private readonly ILogger<WindowsCredentialStorage> _logger;

    public WindowsCredentialStorage(ILogger<WindowsCredentialStorage> logger)
    {
        _logger = logger;
    }

    public async Task StoreCredentialsAsync(string accessToken, string accessTokenSecret, string? userNickname = null)
    {
        try
        {
            var credentials = new StoredCredentials
            {
                AccessToken = accessToken,
                AccessTokenSecret = accessTokenSecret,
                UserNickname = userNickname,
                StoredAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(credentials);
            var encryptedData = ProtectData(json);

            // Store in Windows Credential Manager
            await Task.Run(() => StoreInCredentialManager(encryptedData));

            _logger.LogInformation("OAuth credentials stored securely");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store OAuth credentials");
            throw;
        }
    }

    public async Task<StoredCredentials?> RetrieveCredentialsAsync()
    {
        try
        {
            var encryptedData = await Task.Run(RetrieveFromCredentialManager);
            if (encryptedData == null)
            {
                _logger.LogDebug("No stored credentials found");
                return null;
            }

            var json = UnprotectData(encryptedData);
            var credentials = JsonSerializer.Deserialize<StoredCredentials>(json);

            _logger.LogInformation("OAuth credentials retrieved successfully");
            return credentials;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve OAuth credentials");
            return null;
        }
    }

    public async Task ClearCredentialsAsync()
    {
        try
        {
            await Task.Run(RemoveFromCredentialManager);
            _logger.LogInformation("OAuth credentials cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear OAuth credentials");
            throw;
        }
    }

    public async Task<bool> HasStoredCredentialsAsync()
    {
        try
        {
            var data = await Task.Run(RetrieveFromCredentialManager);
            return data != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check for stored credentials");
            return false;
        }
    }

    private byte[] ProtectData(string data)
    {
        var dataBytes = Encoding.UTF8.GetBytes(data);
        return ProtectedData.Protect(dataBytes, null, DataProtectionScope.CurrentUser);
    }

    private string UnprotectData(byte[] encryptedData)
    {
        var dataBytes = ProtectedData.Unprotect(encryptedData, null, DataProtectionScope.CurrentUser);
        return Encoding.UTF8.GetString(dataBytes);
    }

    private void StoreInCredentialManager(byte[] encryptedData)
    {
        // For now, we'll use a simple file-based approach as a fallback
        // In a production app, you would use the Windows Credential Manager API
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var winmugPath = Path.Combine(appDataPath, "Winmug");
        Directory.CreateDirectory(winmugPath);
        
        var credentialFile = Path.Combine(winmugPath, "credentials.dat");
        File.WriteAllBytes(credentialFile, encryptedData);
    }

    private byte[]? RetrieveFromCredentialManager()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var credentialFile = Path.Combine(appDataPath, "Winmug", "credentials.dat");
        
        if (!File.Exists(credentialFile))
            return null;
            
        return File.ReadAllBytes(credentialFile);
    }

    private void RemoveFromCredentialManager()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var credentialFile = Path.Combine(appDataPath, "Winmug", "credentials.dat");
        
        if (File.Exists(credentialFile))
        {
            File.Delete(credentialFile);
        }
    }
}
