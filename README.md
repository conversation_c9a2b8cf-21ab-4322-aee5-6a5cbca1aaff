# Winmug - SmugMug Photo Downloader

A Windows desktop application for downloading your entire SmugMug photo library to your local computer.

## Features

- **OAuth 1.0a Authentication**: Secure authentication with SmugMug using OAuth 1.0a
- **Complete Library Download**: Downloads your entire SmugMug photo library including all albums and folders
- **Original Quality**: Downloads original/raw versions of all photos
- **Folder Structure Preservation**: Maintains your SmugMug album hierarchy on your local file system
- **Progress Monitoring**: Real-time progress tracking with download speed and estimated time remaining
- **Pause/Resume**: Ability to pause and resume downloads
- **Error Handling**: Robust error handling with detailed logging and retry mechanisms
- **Windows Store Ready**: Designed for distribution through the Windows Marketplace

## Prerequisites

- Windows 10 or Windows 11
- .NET 8.0 Runtime
- SmugMug account with photos to download
- SmugMug API Key (see setup instructions below)

## Setup Instructions

### 1. Get SmugMug API Credentials

1. Visit the [SmugMug API Developer Portal](https://api.smugmug.com/api/developer/apply)
2. Apply for an API key by accepting the terms and conditions
3. Once approved, you'll receive:
   - Consumer Key (API Key)
   - Consumer Secret

### 2. Configure the Application

1. Open the `src/Winmug/appsettings.json` file
2. Replace the placeholder values with your actual API credentials:

```json
{
  "SmugMugOAuth": {
    "ConsumerKey": "YOUR_ACTUAL_CONSUMER_KEY_HERE",
    "ConsumerSecret": "YOUR_ACTUAL_CONSUMER_SECRET_HERE"
  }
}
```

**Important**: Never commit your actual API credentials to version control. Consider using `appsettings.local.json` for local development.

### 3. Build and Run

```bash
# Build the solution
dotnet build

# Run the application
dotnet run --project src/Winmug
```

## Usage

### Authentication

1. Click "Authenticate with SmugMug" button
2. Your default browser will open with the SmugMug authorization page
3. Log in to your SmugMug account and authorize the application
4. Copy the verification code from the browser
5. Paste the verification code into the application and click "Complete Authentication"

### Download Photos

1. After successful authentication, click "Browse..." to select a target directory
2. Choose where you want to save your photos on your local computer
3. Click "Start Download" to begin downloading your entire photo library
4. Monitor progress in real-time with the progress bars and status log
5. Use Pause/Resume/Cancel buttons as needed

### Download Structure

The application will create a folder structure on your local computer that mirrors your SmugMug organization:

```
Target Directory/
├── Folder 1/
│   ├── Album A/
│   │   ├── photo1.jpg
│   │   └── photo2.jpg
│   └── Album B/
│       └── photo3.jpg
└── Folder 2/
    └── Album C/
        └── photo4.jpg
```

## Project Structure

```
Winmug/
├── src/
│   ├── Winmug/                 # WPF Application
│   │   ├── Views/              # XAML Views
│   │   ├── ViewModels/         # MVVM ViewModels
│   │   ├── Converters/         # Value Converters
│   │   └── Styles/             # UI Styles
│   └── Winmug.Core/            # Core Business Logic
│       ├── Authentication/     # OAuth 1.0a Implementation
│       ├── Models/             # Data Models
│       └── Services/           # API Client and Services
├── tests/
│   └── Winmug.Tests/           # Unit Tests
├── PLANNING.md                 # Detailed project planning
└── TASKS.md                    # Implementation tasks
```

## Architecture

The application follows clean architecture principles:

- **Winmug (Presentation Layer)**: WPF application using MVVM pattern
- **Winmug.Core (Business Layer)**: Core business logic, API clients, and services
- **Dependency Injection**: Uses Microsoft.Extensions.DependencyInjection
- **Async/Await**: Fully asynchronous operations for responsive UI
- **Secure Storage**: OAuth tokens stored securely using Windows Data Protection API

## Key Components

### Authentication
- **OAuth 1.0a Implementation**: Custom implementation of OAuth 1.0a for SmugMug
- **Secure Token Storage**: Encrypted storage of access tokens
- **Automatic Token Management**: Handles token lifecycle

### API Client
- **SmugMug API v2**: Full integration with SmugMug's REST API
- **Rate Limiting**: Respects API rate limits
- **Error Handling**: Robust error handling with retry logic

### Download Manager
- **Concurrent Downloads**: Configurable concurrent download limits
- **Progress Tracking**: Real-time progress reporting
- **Resume Support**: Ability to resume interrupted downloads
- **Error Recovery**: Continues downloading other files if individual downloads fail

## Development

### Building from Source

```bash
# Clone the repository
git clone <repository-url>
cd Winmug

# Restore dependencies
dotnet restore

# Build the solution
dotnet build

# Run tests
dotnet test

# Run the application
dotnet run --project src/Winmug
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## Security Considerations

- OAuth tokens are encrypted using Windows Data Protection API
- API credentials should never be committed to version control
- All network communications use HTTPS
- No sensitive data is logged

## Troubleshooting

### Common Issues

1. **Authentication Fails**: Verify your API credentials are correct
2. **Download Errors**: Check your internet connection and SmugMug service status
3. **Permission Errors**: Ensure the target directory is writable
4. **Large Libraries**: For very large libraries, consider downloading in smaller batches

### Logging

The application logs detailed information to help with troubleshooting:
- Authentication events
- API requests and responses
- Download progress and errors
- File system operations

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This application is not affiliated with SmugMug. SmugMug is a trademark of SmugMug, Inc.
