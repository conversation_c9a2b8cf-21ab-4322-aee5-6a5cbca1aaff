Winmug: SmugMug Photo Downloader - Task List
Phase 1: Core Authentication and Album Structure
Task 1.1: Register the application on the SmugMug developer portal.
Task 1.2: Research and understand the SmugMug OAuth 2.0 flow and required scopes.
Task 1.3: Implement the UI for initiating the SmugMug authorization (button).
Task 1.4: Implement the handling of the SmugMug authorization callback (local server or browser-based).
Task 1.5: Implement the exchange of the authorization code for Access and Refresh Tokens.
Task 1.6: Implement secure storage of Access and Refresh Tokens (e.g., Windows Credential Manager).
Task 1.7: Implement logic to refresh the Access Token when it expires.
Task 1.8: Research and identify the SmugMug API endpoints for retrieving the user's album hierarchy.
Task 1.9: Define the data model in C# to represent SmugMug albums and their relationships.
Task 1.10: Implement the logic to fetch the top-level albums for the authenticated user using the SmugMug API.
Task 1.11: Implement the logic to recursively traverse and retrieve all subalbums.
Task 1.12: Display the retrieved album structure (e.g., in a tree view - could be a later UI enhancement, for now focus on data retrieval).
Phase 2: Raw Photo Download Logic
Task 2.1: Research the SmugMug API endpoint for listing photos within an album.
Task 2.2: Identify how to retrieve the download URL for the original/raw version of a photo from the API response.
Task 2.3: Implement the logic to fetch the list of photos for a given album.
Task 2.4: Implement the logic to extract the raw photo download URLs.
Task 2.5: Implement a basic download function for a single photo using its URL and HttpClient.
Task 2.6: Allow the user to select a local target download folder using FolderBrowserDialog.
Task 2.7: Implement the creation of local directories mirroring the SmugMug album structure.
Task 2.8: Implement the core logic to iterate through the fetched albums and download all raw photos within them to the corresponding local folders.
Phase 3: Progress Monitoring and Basic Error Handling
Task 3.1: Implement basic progress tracking for individual file downloads (bytes downloaded vs. total bytes).
Task 3.2: Implement an overall progress indicator (e.g., number of albums completed).
Task 3.3: Implement basic error handling (try-catch) around API calls and file downloads.
Task 3.4: Log any errors encountered to a debug output or a simple log file.
Task 3.5: Display simple error messages in the UI if critical failures occur.
Phase 4: Pause/Resume and Enhanced Error Handling
Task 4.1: Implement "Pause" functionality to halt ongoing downloads and store the current download state.
Task 4.2: Implement "Resume" functionality to continue downloads from the paused state.
Task 4.3: Implement more robust error handling for network issues, API errors, and file system errors.
Task 4.4: Implement per-file and per-album error tracking. If a download fails, log the details and continue with the next item.
Task 4.5: Implement the display of an estimated time remaining for the download process.
Task 4.6: Implement a summary view at the end of the download process showing successfully downloaded items and any failures (with error messages).
Phase 5: User Interface Development (WPF)
Task 5.1: Design and build the main application window in WPF with the necessary UI elements (buttons, progress bars, status display).
Task 5.2: Bind the UI elements to the application's data and download logic (using MVVM pattern is recommended for maintainability).
Task 5.3: Implement visual feedback in the UI to reflect the download progress and status updates.
Task 5.4: Ensure the UI remains responsive during API calls and downloads by using asynchronous operations (Tasks).
Phase 6: Windows Marketplace Preparation and Testing
Task 6.1: Thoroughly review the Windows Marketplace developer guidelines.
Task 6.2: Implement code signing for the application.
Task 6.3: Create the application manifest file.
Task 6.4: Conduct thorough testing of the application on various Windows versions and scenarios.
Task 6.5: Package the application using MSIX.
Task 6.6: Create a privacy policy if necessary.
Task 6.7: Prepare the application listing for the Windows Marketplace (description, screenshots, etc.).
Task 6.8: Submit the application to the Windows Marketplace for review.