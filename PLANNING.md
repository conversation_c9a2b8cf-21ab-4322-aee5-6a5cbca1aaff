# Winmug: SmugMug Photo Downloader - Windows Desktop Application

## 1. Overview

This application will allow users to securely download their entire photo library and album structure from SmugMug to their local Windows computer. The application will utilize SmugMug's API with proper user authentication (OAuth 1.0a), mirror the album hierarchy on the local file system, provide download progress with estimated time, offer pause/resume functionality, and gracefully handle errors, continuing the download process for other albums and photos. The application will be designed for distribution through the Windows Marketplace.

## 2. Goals

- **User-Friendly Authentication**: Implement a straightforward OAuth 1.0a flow for secure SmugMug access
- **Complete Structure Download**: Replicate the user's entire SmugMug album and subalbum structure locally
- **Raw Photo Download**: Download the original/raw versions of all photos
- **Target Folder Selection**: Allow users to choose the local directory for saving their photos
- **Progress Monitoring**: Provide clear visual progress of the download process, including estimated time remaining
- **Pause and Resume**: Enable users to pause and resume the download at any point
- **Robust Error Handling**: Implement error detection and logging for API calls, downloads, and file system operations, ensuring the process continues for other items
- **Error Reporting**: Display a summary of any failed downloads (albums or individual photos) at the end of the process
- **Windows Marketplace Compliance**: Adhere to Windows Marketplace guidelines for application development and publishing

## 3. Technology Stack

- **Programming Language**: C# (.NET 8.0) - Well-suited for Windows desktop development and marketplace integration
- **UI Framework**: WPF (Windows Presentation Foundation) - Offers a rich and modern UI experience with good data binding capabilities
- **HTTP Client**: HttpClient class in .NET for making API requests to SmugMug
- **JSON Handling**: System.Text.Json for parsing SmugMug API responses
- **Authentication Library**: Custom implementation for the OAuth 1.0a flow (SmugMug uses OAuth 1.0a, not 2.0)
- **Threading/Async**: Task Parallel Library (TPL) for managing asynchronous operations (API calls, downloads) to keep the UI responsive
- **Secure Storage**: Windows Credential Manager for storing OAuth tokens securely
## 4. Detailed Plan

### 4.1. Authentication (OAuth 1.0a)

**SmugMug Application Registration:**
- Register the application on the SmugMug developer portal to obtain a Client ID and Client Secret
- Define the Redirect URI (callback URL) for the OAuth flow

**OAuth 1.0a Implementation:**
- **Request Token**: Obtain a request token from `https://api.smugmug.com/services/oauth/1.0a/getRequestToken`
- **User Authorization**: Redirect user to `https://api.smugmug.com/services/oauth/1.0a/authorize` with appropriate parameters
- **Handle Callback**: For desktop app, use `oauth_callback=oob` to get a verification code that user enters manually
- **Access Token**: Exchange verification code for access token using `https://api.smugmug.com/services/oauth/1.0a/getAccessToken`
- **Secure Token Storage**: Store the Access Token securely using Windows Credential Manager
- **Token Management**: OAuth 1.0a access tokens don't expire (unless revoked by user)

### 4.2. Album Structure Retrieval

**API Endpoints:**
- Start with authenticated user: `/api/v2!authuser`
- Get user's root node: Follow `Node` link from user endpoint
- Traverse hierarchy: Use `ChildNodes` link to get folders and albums recursively

**Data Model:**
- Define data structures to represent SmugMug nodes (folders/albums) and their relationships
- Support hierarchical structure with parent-child relationships
- Track node types (Folder vs Album)

**Recursive Traversal:**
- Implement recursive algorithm to fetch all nodes starting from root
- Handle pagination for large folder structures
- Create local folder mapping based on SmugMug hierarchy

### 4.3. Raw Photo Download

**API Endpoints:**
- Get album images: Use `AlbumImages` link from album nodes
- Get download URLs: Use `ImageSizeDetails` link from image objects to get original/raw URLs
- Handle authentication for private content

**Download Implementation:**
- **Download Manager**: Implement concurrent download manager with configurable limits
- **Progress Tracking**: Track bytes downloaded vs total bytes for each file
- **Estimated Time**: Calculate ETA based on recent download speeds
- **Pause/Resume**: Store download state and support resuming from partial downloads
- **Error Handling**: Robust error handling with retry logic and detailed logging
### 4.4. Local File System Operations

**Directory Creation:**
- Use .NET's Directory class to create local folders mirroring SmugMug album structure
- Handle invalid characters in folder names
- Ensure proper path length limits on Windows

**File Saving:**
- Use HttpClient and FileStream for efficient file downloads
- Implement proper file naming and conflict resolution
- Support resume for partially downloaded files

### 4.5. User Interface (WPF)

**Main Window Components:**
- **Authentication Section**: Button to initiate SmugMug authentication, display username
- **Target Selection**: Folder browser dialog for selecting download location
- **Download Controls**: Start, Pause, Resume, Cancel buttons with proper state management
- **Progress Display**:
  - Overall progress bar (albums/files completed)
  - Current file progress bar
  - Estimated time remaining
  - Download speed indicator
- **Status Display**: Real-time log of operations and any errors
- **Summary View**: Final report of successful downloads and failures

**UI Architecture:**
- MVVM pattern for clean separation of concerns
- Async/await for responsive UI during long operations
- Data binding for real-time updates
- Proper error handling and user feedback

### 4.6. Error Handling and Reporting

**Centralized Logging:**
- Structured logging to file and in-memory
- Different log levels (Info, Warning, Error)
- Detailed error context for troubleshooting

**User-Friendly Error Display:**
- Clear error messages in UI
- Retry mechanisms for transient failures
- Graceful degradation when possible

**Final Summary:**
- Comprehensive report of download results
- Failed items with specific error reasons
- Statistics (total files, success rate, etc.)

### 4.7. Windows Marketplace Preparation

**Compliance Requirements:**
- Review Windows Store policies and guidelines
- Implement proper app manifest and metadata
- Code signing with valid certificate
- MSIX packaging for store distribution

**Testing Strategy:**
- Test on multiple Windows versions (10, 11)
- Various screen resolutions and DPI settings
- Different network conditions
- Large photo libraries for performance testing

**Privacy and Security:**
- Privacy policy for data handling
- Secure credential storage
- No unnecessary permissions
## 5. Risks and Challenges

- **SmugMug API Changes**: API could change, breaking the application. Need regular monitoring and version management
- **Rate Limiting**: SmugMug API has rate limits. Implement proper throttling and retry logic
- **Large Photo Libraries**: Very large libraries require efficient memory management and progress tracking
- **Network Connectivity**: Intermittent network issues need robust retry mechanisms and pause/resume
- **Authentication Security**: Secure handling and storage of OAuth 1.0a tokens is critical
- **Windows Marketplace Approval**: Must meet all store requirements for successful publishing
- **Performance**: Concurrent downloads must be balanced with system resources and API limits

## 6. SmugMug API Key Information

**Required API Endpoints:**
- OAuth 1.0a endpoints for authentication
- `/api/v2!authuser` - Get authenticated user
- `/api/v2/user/{username}/node` - Get user's root node
- `/api/v2/node/{nodeId}!children` - Get child nodes (folders/albums)
- `/api/v2/album/{albumId}!images` - Get images in album
- `/api/v2/image/{imageId}!sizedetails` - Get download URLs

**Authentication Flow:**
1. Request token from SmugMug
2. User authorizes in browser
3. User enters verification code
4. Exchange for access token
5. Store token securely

## 7. Future Enhancements (Out of Scope for Initial Release)

- **Selective Download**: Allow users to choose specific albums or photos
- **Download Filters**: Implement filters based on date, tags, etc.
- **Metadata Download**: Option to download metadata (captions, keywords) along with photos
- **Background Download**: Continue downloads when application is minimized
- **Incremental Sync**: Only download new/changed photos
- **Multiple Account Support**: Support multiple SmugMug accounts