using System.Windows;
using Winmug.ViewModels;

namespace Winmug.Views;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        
        // Initialize the view model
        Loaded += async (s, e) => await viewModel.InitializeAsync();
    }
}
